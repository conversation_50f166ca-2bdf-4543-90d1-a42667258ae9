#!/usr/bin/env python3
"""
Debug script to check what API_URL is being used
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check API_URL
api_url = os.getenv("API_URL", "http://127.0.0.1:8000")

print("=== API_URL Debug Information ===")
print(f"API_URL from environment: {repr(os.getenv('API_URL'))}")
print(f"API_URL with default: {repr(api_url)}")
print(f"Full hospital requests URL would be: {api_url}/api/hospital/requests")

# Check all environment variables that contain 'api'
print("\n=== All environment variables containing 'api' ===")
for key, value in os.environ.items():
    if 'api' in key.lower():
        print(f"{key}: {value}")

# Test the actual URL construction like in main.py
print("\n=== URL Construction Test ===")
test_wallet = "******************************************"
full_url = f"{api_url}/api/hospital/requests?wallet_address={test_wallet}"
print(f"Constructed URL: {full_url}")

# Check if the URL has double 'api'
if '/api/api/' in full_url:
    print("❌ PROBLEM FOUND: Double 'api' in URL!")
    print("This suggests API_URL already contains '/api' at the end")
else:
    print("✅ URL looks correct")
